
#include <iostream>
#include <ggml/ggml.h>
#include <ggml/ggml-backend.h>

int main() {
    std::cout << "Testing CUDA debug log output..." << std::endl;
    
    // 初始化GGML
    ggml_init_params params = {
        .mem_size   = 16 * 1024 * 1024,
        .mem_buffer = NULL,
        .no_alloc   = false,
    };
    
    struct ggml_context * ctx = ggml_init(params);
    if (!ctx) {
        std::cerr << "Failed to initialize GGML context" << std::endl;
        return 1;
    }
    
    std::cout << "GGML context initialized successfully" << std::endl;
    
    // 获取CUDA后端数量
    int cuda_device_count = ggml_backend_cuda_get_device_count();
    std::cout << "CUDA devices available: " << cuda_device_count << std::endl;
    
    if (cuda_device_count < 2) {
        std::cout << "Need at least 2 CUDA devices to test device mismatch" << std::endl;
        ggml_free(ctx);
        return 0;
    }
    
    // 初始化两个不同设备的后端
    ggml_backend_t backend0 = ggml_backend_cuda_init(0);
    ggml_backend_t backend1 = ggml_backend_cuda_init(1);
    
    if (!backend0 || !backend1) {
        std::cerr << "Failed to initialize CUDA backends" << std::endl;
        ggml_free(ctx);
        return 1;
    }
    
    std::cout << "CUDA backends initialized successfully" << std::endl;
    
    // 创建张量
    struct ggml_tensor * tensor0 = ggml_new_tensor_1d(ctx, GGML_TYPE_F32, 1000);
    struct ggml_tensor * tensor1 = ggml_new_tensor_1d(ctx, GGML_TYPE_F32, 1000);
    
    // 创建不同设备的缓冲区
    ggml_backend_buffer_t buffer0 = ggml_backend_cuda_buffer_init(0, ggml_nbytes(tensor0));
