# Tensor Parallelism Implementation Design

## Current Issues Analysis

### Root Causes Identified:
1. **Computation Skipping**: All distributed tensor operations (ADD, MUL_MAT, RMS_NORM) are being skipped and falling back to CPU
2. **Memory Management**: Tensors are distributed across GPUs but computation kernels don't handle cross-GPU operations
3. **NCCL Communication**: AllReduce operations are disabled, preventing proper result aggregation
4. **Peer Access**: Cross-GPU memory access causes CUDA illegal memory access errors
5. **Incomplete Kernels**: CUDA kernels lack distributed computation logic

## Proposed Solution Strategy

### Phase 1: Fix Memory Management and Peer Access
- Implement proper CUDA peer access setup between GPUs
- Fix memory allocation to ensure tensors are accessible across devices
- Add proper error handling for cross-GPU memory operations

### Phase 2: Implement Distributed Computation Logic
- **Column-split tensors**: No communication needed, compute locally on each GPU
- **Row-split tensors**: Require AllReduce after computation to combine results
- **Replicated tensors**: Keep full copies on all GPUs

### Phase 3: Enable NCCL Communication
- Re-enable AllReduce operations with proper error handling
- Implement AllGather for tensors that need full reconstruction
- Add proper stream synchronization

### Phase 4: Update Computation Kernels
- Modify MUL_MAT to handle distributed tensors correctly
- Update ADD and RMS_NORM for distributed operations
- Add tensor splitting/combining logic in kernels

## Implementation Plan

### Step 1: Enable Peer Access (CRITICAL)
```cpp
// In ggml_cuda_tp_init_peer_access()
for (int i = 0; i < tp_size; i++) {
    for (int j = 0; j < tp_size; j++) {
        if (i != j) {
            cudaSetDevice(device_ids[i]);
            cudaDeviceEnablePeerAccess(device_ids[j], 0);
        }
    }
}
```

### Step 2: Fix Memory Allocation Strategy
- Instead of round-robin distribution, implement proper tensor splitting
- Column-split: Split tensor along dimension 1, allocate split portion on each GPU
- Row-split: Split tensor along dimension 0, allocate split portion on each GPU
- Replicated: Allocate full tensor on all GPUs

### Step 3: Implement Distributed Computation
- Remove the skipping logic in ggml-cuda.cu
- Add proper distributed computation paths for each operation type
- Implement AllReduce for row-split operations

### Step 4: Enable NCCL Communication
- Re-enable AllReduce in tensor-parallel.cu
- Add proper error handling and fallback mechanisms
- Implement stream synchronization

## Testing Strategy

### Incremental Testing:
1. Test peer access setup with simple memory copy operations
2. Test distributed memory allocation without computation
3. Test column-split operations (no communication needed)
4. Test row-split operations with AllReduce
5. Full integration test with multi-layer model

### Validation Commands:
```bash
# Baseline (should work)
CUDA_VISIBLE_DEVICES="0,1" ./build-debug/bin/llama-cli -m tinyllama-1.1b-chat-v1.0.Q4_K_M.gguf --gpus-tp 1 -ngl 10 -p "Hello" -n 20 -no-cnv

# Target (should produce identical output)
CUDA_VISIBLE_DEVICES="0,1" ./build-debug/bin/llama-cli -m tinyllama-1.1b-chat-v1.0.Q4_K_M.gguf --gpus-tp 2 -ngl 10 -p "Hello" -n 20 -no-cnv
```

## Success Criteria
1. No CUDA memory access errors
2. Identical output between --gpus-tp 1 and --gpus-tp 2
3. Both GPUs show utilization during inference
4. Performance improvement with multi-GPU setup
