# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build Commands

### Standard Build
```bash
# Basic build
cmake -B build
cmake --build build --config Release

# With parallel compilation
cmake --build build --config Release -j $(nproc)

# Debug build (enables debug logging)
cmake -B build-debug -DCMAKE_BUILD_TYPE=Debug
cmake --build build-debug

# Release build (disables debug logging)
cmake -B build-release -DCMAKE_BUILD_TYPE=Release
cmake --build build-release
```

### GPU Backend Builds
```bash
# CUDA
cmake -B build -DGGML_CUDA=ON
cmake --build build --config Release

# CUDA (sm75 only) - Debug build
cmake -B build-debug -DCMAKE_BUILD_TYPE=Debug -DGGML_CUDA=ON -DGGML_CUDA_FA=OFF -DCMAKE_CUDA_ARCHITECTURES=75
cmake --build build-debug

# CUDA (sm75 only) - Release build
cmake -B build-release -DCMAKE_BUILD_TYPE=Release -DGGML_CUDA=ON -DGGML_CUDA_FA=OFF -DCMAKE_CUDA_ARCHITECTURES=75
cmake --build build-release

# Metal (Apple Silicon)
cmake -B build -DGGML_METAL=ON
cmake --build build --config Release

# Vulkan
cmake -B build -DGGML_VULKAN=ON
cmake --build build --config Release

# SYCL (Intel/AMD)
cmake -B build -DGGML_SYCL=ON -DCMAKE_C_COMPILER=icx -DCMAKE_CXX_COMPILER=icpx
cmake --build build --config Release
```

### Build with Specific Features
```bash
# Build server component
cmake -B build -DLLAMA_BUILD_SERVER=ON
cmake --build build --config Release

# Build shared library
cmake -B build -DBUILD_SHARED_LIBS=ON
cmake --build build --config Release

# Build with OpenBLAS
cmake -B build -DGGML_BLAS=ON -DGGML_BLAS_VENDOR=OpenBLAS
cmake --build build --config Release
```

## Test Commands

### Integration Tests
```bash
# Tensor parallelism tests
./tests/test-tensor-parallel-integration.sh

# Server tests (requires pytest)
cd tools/server/tests && ./tests.sh

# Tokenizer tests
python tests/test-tokenizer-0.py
```

### Manual Testing
```bash
# Test llama-cli with model
./build/bin/llama-cli -m model.gguf --prompt "Hello" -n 10

# Test server
./build/bin/llama-server -m model.gguf --port 8080

# Benchmark
./build/bin/llama-bench -m model.gguf

# Test tensor parallelism
CUDA_VISIBLE_DEVICES="0,1" ./build/bin/llama-cli -m model.gguf --gpus-tp 2 -ngl 10 -p "Hello" -n 5

# Test multi-GPU tensor parallelism
CUDA_VISIBLE_DEVICES="0,1,2,3" ./build/bin/llama-cli -m model.gguf --gpus-tp 4 -ngl 20 -p "Hello" -n 5

# Test tensor parallelism with llama-bench
./build/bin/llama-bench -m model.gguf --gpus-tp 2 -ngl 10 -n 128 -p 512
```

## Code Architecture

### Core Components
- **src/**: Main library implementation
  - `llama.cpp`: Core interface implementation
  - `llama-model.cpp`: Model loading and management
  - `llama-context.cpp`: Inference context handling
  - `llama-vocab.cpp`: Vocabulary and tokenization
  - `llama-grammar.cpp`: Grammar constraint support

- **include/**: Public API headers
  - `llama.h`: Main public API
  - `llama-cpp.h`: C++ API

- **ggml/**: GGML tensor library (external dependency)
  - Core tensor operations and backends
  - GPU support via CUDA, Metal, Vulkan, etc.

### Tools
- **tools/main/**: `llama-cli` - Command line interface
- **tools/server/**: `llama-server` - HTTP API server
- **tools/quantize/**: Model quantization tools
- **tools/llama-bench/**: Performance benchmarking
- **tools/perplexity/**: Perplexity measurement

### Key Features
- Tensor parallelism support (`--gpus-tp` parameter)
- Multiple GPU backend support (CUDA, Metal, Vulkan, SYCL)
- Model quantization (2-8 bit precision)
- Grammar-based output constraints
- HTTP server with OpenAI-compatible API
- Multimodal support (vision + language)

## Development Patterns

### Adding New Features
1. Add API definitions in `include/llama.h`
2. Implement core functionality in `src/llama-*.cpp` files
3. Add command line support in `tools/main/llama-cli.cpp`
4. Add server support in `tools/server/` if applicable
5. Write integration tests in `tests/` directory

### Backend Integration
- GPU backends integrate through GGML backend system
- Tensor operations are implemented in `ggml/src/`
- Backend-specific code in `ggml/src/ggml-*/` directories

### Model Support
- Model architecture definitions in `src/llama-arch.cpp`
- Model loading in `src/llama-model-loader.cpp`
- Quantization in `src/llama-quant.cpp`

## Common Development Tasks

### Building Specific Targets
```bash
# Build only llama-cli
cmake --build build --target llama-cli

# Build only server
cmake --build build --target llama-server

# Build all tools
cmake --build build --target all
```

### Debug Build with Warnings
```bash
cmake -B build -DCMAKE_BUILD_TYPE=Debug -DLLAMA_FATAL_WARNINGS=ON
cmake --build build --target llama-cli
```

### Running with Tensor Parallelism
```bash
# 2-way tensor parallelism
./build/bin/llama-cli -m model.gguf --gpus-tp 2 --tensor-split 8,8

# 4-way with specific GPU assignment
./build/bin/llama-cli -m model.gguf --gpus-tp 4 --tensor-split 4,4,4,4 --main-gpu 0
```

### Testing Server API
```bash
# Start server
./build/bin/llama-server -m model.gguf --port 8080

# Test API
curl http://localhost:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{"messages": [{"role": "user", "content": "Hello"}]}'
```

### Tensor Parallelism Testing
```bash
# Test single GPU (working)
CUDA_VISIBLE_DEVICES="0,1" ./build-debug/bin/llama-cli -m tinyllama-1.1b-chat-v1.0.Q4_K_M.gguf --gpus-tp 1 -ngl 10 -p "Hello" -n 20 -no-cnv

# Test 2-way tensor parallelism (now working)
CUDA_VISIBLE_DEVICES="0,1" ./build-debug/bin/llama-cli -m tinyllama-1.1b-chat-v1.0.Q4_K_M.gguf --gpus-tp 2 -ngl 10 -p "Hello" -n 20 -no-cnv

# Test with same seed to verify identical output
CUDA_VISIBLE_DEVICES="0,1" ./build-debug/bin/llama-cli -m tinyllama-1.1b-chat-v1.0.Q4_K_M.gguf --gpus-tp 1 -ngl 10 -p "Hello" -n 10 -no-cnv --seed 12345
CUDA_VISIBLE_DEVICES="0,1" ./build-debug/bin/llama-cli -m tinyllama-1.1b-chat-v1.0.Q4_K_M.gguf --gpus-tp 2 -ngl 10 -p "Hello" -n 10 -no-cnv --seed 12345

# Debug version with verbose output
CUDA_VISIBLE_DEVICES="0,1" ./build-debug/bin/llama-cli -m tinyllama-1.1b-chat-v1.0.Q4_K_M.gguf --gpus-tp 1 -ngl 10 -p "Hello" -n 20 -no-cnv -v
```

### Working Features
- **Tensor Parallelism (--gpus-tp > 1)**: Now working with conservative replication strategy
- **Multi-GPU Support**: Both GPUs are utilized for model loading and computation
- **Identical Output**: Single GPU and multi-GPU configurations produce identical results with same seed
- **Stability**: No crashes or memory access errors with the current implementation

### Implementation Notes
- **Peer Access**: When peer access is not supported between GPUs, the implementation falls back to tensor replication strategy
- **Memory Distribution**: Model tensors are distributed across available GPUs to reduce per-GPU memory usage
- **Conservative Approach**: Current implementation prioritizes stability over maximum performance optimization