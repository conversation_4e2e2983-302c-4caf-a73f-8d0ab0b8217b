
#include <stdio.h>
#include <stdlib.h>
#include "ggml/ggml.h"
#include "ggml/ggml-backend.h"

// 简单的测试程序来触发CUDA设备不匹配的调试日志
int main() {
    // 初始化GGML
    ggml_init_params params = {
        .mem_size   = 16 * 1024 * 1024,
        .mem_buffer = NULL,
        .no_alloc   = false,
    };
    
    struct ggml_context * ctx = ggml_init(params);
    if (!ctx) {
        fprintf(stderr, "Failed to initialize GGML context\n");
        return 1;
    }
    
    printf("GGML context initialized successfully\n");
    
    // 尝试创建CUDA后端
    ggml_backend_t backend = ggml_backend_cuda_init(0); // 使用设备0
    if (!backend) {
        fprintf(stderr, "Failed to initialize CUDA backend\n");
        ggml_free(ctx);
        return 1;
    }
    
    printf("CUDA backend initialized successfully\n");
    
    // 创建缓冲区
    size_t buffer_size = 1024 * 1024; // 1MB
    ggml_backend_buffer_t buffer = ggml_backend_cuda_buffer_init(1, buffer_size); // 使用设备1
    
    if (!buffer) {
        fprintf(stderr, "Failed to create CUDA buffer\n");
        ggml_backend_free(backend);
        ggml_free(ctx);
        return 1;
    }
    
    printf("CUDA buffer created successfully\n");
    
    // 这里应该会触发设备不匹配的调试日志
